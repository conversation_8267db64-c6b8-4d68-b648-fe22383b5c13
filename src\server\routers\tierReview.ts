import { z } from 'zod';

import { TRPCError } from '@trpc/server';

import { kysely } from '@/database/kysely';

import { createRouter, publicProcedure } from '../trpc';

export const tierReviewRouter = createRouter({
  // ---------------------------
  createOrUpdate: publicProcedure
    .input(
      z.object({
        gameSteamId: z.number().positive(),
        reviewTitle: z.string().optional(),
        reviewContent: z.string().optional(),
        publishDate: z.string().optional(),
      }),
    )
    .mutation(
      async ({
        input: { gameSteamId, reviewTitle, reviewContent, publishDate },
        ctx: { user },
      }) => {
        if (!user) {
          throw new TRPCError({ code: 'UNAUTHORIZED' });
        }

        // Upsert into tierReview
        const result = await kysely
          .insertInto('tierReview')
          .values({
            gameSteamId: gameSteamId,
            reviewTitle: reviewTitle ?? null,
            reviewContent: reviewContent ?? null,
            publishDate: publishDate ?? null,
          })
          .onConflict(oc =>
            oc.column('gameSteamId').doUpdateSet({
              reviewTitle: eb => eb.ref('excluded.reviewTitle'),
              reviewContent: eb => eb.ref('excluded.reviewContent'),
              publishDate: eb => eb.ref('excluded.publishDate'),
            }),
          )
          .returningAll()
          .executeTakeFirst();

        return result;
      },
    ),
});

export type TierReviewRouter = typeof tierReviewRouter;
