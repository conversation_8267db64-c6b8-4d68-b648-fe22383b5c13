<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core';

import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';
import type { GameSteamWithRating } from '@/stores/item';

const props = defineProps<{
  game: GameSteamWithRating;
}>();

const emit = defineEmits<{
  (e: 'scoreUpdated', scoreType: string, score: number): void;
}>();

// Score definitions with labels and colors
const scoreDefinitions = [
  { key: 'scoreAudio', label: 'Audio', icon: '🎵', color: '#8B5CF6' },
  { key: 'scoreBias', label: 'Bias', icon: '⚖️', color: '#EF4444' },
  { key: 'scoreBuy', label: 'Buy', icon: '💰', color: '#10B981' },
  { key: 'scoreControl', label: 'Control', icon: '🎮', color: '#3B82F6' },
  { key: 'scoreCreative', label: 'Creative', icon: '🎨', color: '#F59E0B' },
  { key: 'scoreFun', label: 'Fun', icon: '😄', color: '#EC4899' },
  { key: 'scoreGraphic', label: 'Graphic', icon: '🖼️', color: '#06B6D4' },
  { key: 'scorePotential', label: 'Potential', icon: '🚀', color: '#84CC16' },
  { key: 'scoreStability', label: 'Stability', icon: '🛡️', color: '#6B7280' },
  { key: 'scoreUi', label: 'UI', icon: '📱', color: '#8B5CF6' },
] as const;

// Reactive scores object
const scores = ref<Record<string, number>>({});

// Initialize scores from game's tierReview
watchEffect(() => {
  if (props.game?.tierReview) {
    scoreDefinitions.forEach(({ key }) => {
      scores.value[key] = props.game.tierReview?.[key] ?? 50;
    });
  } else {
    // Default scores if no tierReview exists
    scoreDefinitions.forEach(({ key }) => {
      scores.value[key] = 50;
    });
  }
});

// Debounced update function
const debouncedUpdate = useDebounceFn(async (scoreType: string, score: number) => {
  try {
    await trpc.tierReview.updateScore.mutate({
      gameSteamId: props.game.steamId,
      scoreType: scoreType as any,
      score: score,
    });
    emit('scoreUpdated', scoreType, score);
  } catch (error) {
    console.error('Failed to update score:', error);
  }
}, 1000);

// Haptic feedback function
const triggerHapticFeedback = (intensity: number) => {
  if ('vibrate' in navigator) {
    // Scale vibration duration based on score (higher score = longer vibration)
    const duration = Math.floor((intensity / 100) * 200) + 50; // 50-250ms
    navigator.vibrate(duration);
  }
};

// Handle slider change
const handleSliderChange = (scoreType: string, value: number) => {
  scores.value[scoreType] = value;
  triggerHapticFeedback(value);
  debouncedUpdate(scoreType, value);
};
</script>

<template>
  <div
    :class="
      cn(
        'grid grid-cols-2 gap-3 p-4',
        'bg-gray-900/95 backdrop-blur-md',
        'rounded-lg border border-gray-700',
        'max-h-96 overflow-y-auto',
      )
    "
  >
    <div
      v-for="scoreDef in scoreDefinitions"
      :key="scoreDef.key"
      :class="cn('flex flex-col gap-2')"
    >
      <!-- Score label and value -->
      <div :class="cn('flex items-center justify-between')">
        <div :class="cn('flex items-center gap-2')">
          <span :class="cn('text-lg')">{{ scoreDef.icon }}</span>
          <span :class="cn('text-sm font-medium text-gray-300')">
            {{ scoreDef.label }}
          </span>
        </div>
        <span
          :class="cn('rounded-full px-2 py-1 text-sm font-bold')"
          :style="{
            backgroundColor: `${scoreDef.color}20`,
            color: scoreDef.color,
            border: `1px solid ${scoreDef.color}40`,
          }"
        >
          {{ scores[scoreDef.key] }}
        </span>
      </div>

      <!-- Slider -->
      <div :class="cn('relative')">
        <input
          :value="scores[scoreDef.key]"
          type="range"
          min="0"
          max="100"
          step="1"
          :class="
            cn(
              'h-2 w-full cursor-pointer appearance-none rounded-lg',
              'bg-gray-700 outline-none',
              'slider-thumb',
            )
          "
          :style="{
            background: `linear-gradient(to right, ${scoreDef.color}40 0%, ${scoreDef.color} ${scores[scoreDef.key]}%, #374151 ${scores[scoreDef.key]}%, #374151 100%)`,
          }"
          @input="
            e => handleSliderChange(scoreDef.key, Number((e.target as HTMLInputElement).value))
          "
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: currentColor;
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider-thumb::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.slider-thumb::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: currentColor;
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider-thumb::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}
</style>
