<script setup lang="ts">
import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';
import type { GameSteamWithRating } from '@/stores/item';

const props = defineProps<{
  game: GameSteamWithRating;
}>();

const emit = defineEmits<{
  (e: 'scoreUpdated', scoreType: string, score: number): void;
}>();

// Score definitions with labels and colors
const scoreDefinitions = [
  { key: 'scoreBias', label: 'Bias', icon: '⚖️', color: '#EF4444' },
  { key: 'scoreCreative', label: 'Creative', icon: '🎨', color: '#F59E0B' },
  { key: 'scoreFun', label: 'Fun', icon: '😄', color: '#EC4899' },
  { key: 'scorePotential', label: 'Potential', icon: '🚀', color: '#84CC16' },
  { key: 'scoreGraphic', label: 'Graphic', icon: '🖼️', color: '#' },
  { key: 'scoreUi', label: 'UI', icon: '📱', color: '#8B5CF6' },
  { key: 'scoreAudio', label: 'Audio', icon: '🎵', color: '#FB5CF6' },
  { key: 'scoreControl', label: 'Control', icon: '🎮', color: '#3B82F6' },
  { key: 'scoreStability', label: 'Stability', icon: '🛡️', color: '#6B7280' },
  { key: 'scoreBuy', label: 'Buy', icon: '💰', color: '#10B981' },
] as const;

// Reactive scores object
const scores = reactive<Record<string, number>>({
  ...scoreDefinitions.reduce((acc, { key }) => ({ ...acc, [key]: 0 }), {}),
});

// Track which sliders are being dragged
const isDragging = reactive<Record<string, boolean>>({
  ...scoreDefinitions.reduce((acc, { key }) => ({ ...acc, [key]: false }), {}),
});

// Track shake animations
const shakeAnimations = reactive<Record<string, boolean>>({
  ...scoreDefinitions.reduce((acc, { key }) => ({ ...acc, [key]: false }), {}),
});

// Track feedback states
const feedbackStates = reactive<Record<string, 'success' | 'error' | 'loading' | null>>({
  ...scoreDefinitions.reduce((acc, { key }) => ({ ...acc, [key]: null }), {}),
});

// Store original values for rollback on error
const originalScores = reactive<Record<string, number>>({
  ...scoreDefinitions.reduce((acc, { key }) => ({ ...acc, [key]: 0 }), {}),
});

// Initialize scores from game's tierReview
watchEffect(() => {
  if (props.game?.tierReview) {
    scoreDefinitions.forEach(({ key }) => {
      scores[key] = props.game.tierReview?.[key] ?? 0;
      originalScores[key] = props.game.tierReview?.[key] ?? 0;
      isDragging[key] = false;
      shakeAnimations[key] = false;
    });
  } else {
    // Default scores if no tierReview exists
    scoreDefinitions.forEach(({ key }) => {
      scores[key] = 0;
      originalScores[key] = 0;
      isDragging[key] = false;
      shakeAnimations[key] = false;
    });
  }
});

// Update function (called when mouse is released)
const updateScore = async (scoreType: string, score: number) => {
  try {
    feedbackStates[scoreType] = 'loading';
    await trpc.tierReview.updateScore.mutate({
      gameSteamId: props.game.steamId,
      scoreType: scoreType as any,
      score: score,
    });

    // Show success feedback
    feedbackStates[scoreType] = 'success';
    setTimeout(() => {
      feedbackStates[scoreType] = null;
    }, 1000);

    // Update original score for future rollbacks
    originalScores[scoreType] = score;
    emit('scoreUpdated', scoreType, score);
  } catch (error) {
    console.error('Failed to update score:', error);

    // Show error feedback
    feedbackStates[scoreType] = 'error';
    setTimeout(() => {
      feedbackStates[scoreType] = null;
    }, 1000);

    // Rollback to original value
    scores[scoreType] = originalScores[scoreType];
  }
};

// Haptic feedback (during drag)
const triggerHapticFeedback = (intensity: number) => {
  if ('vibrate' in navigator) {
    // Scale vibration duration based on score (higher score = longer vibration)
    const duration = Math.floor((intensity / 100) * 200) + 50; // 50-250ms
    navigator.vibrate(duration);
  }
};

// Shake animation (on release)
const triggerShakeAnimation = (scoreType: string, intensity: number) => {
  shakeAnimations[scoreType] = true;
  setTimeout(
    () => {
      shakeAnimations[scoreType] = false;
    },
    300 + (intensity / 100) * 200,
  ); // 300-500ms shake duration
};

// Get dynamic color based on score
const getDynamicColor = (baseColor: string, score: number) => {
  // Convert score to saturation (0-100 -> 30-100)
  const saturation = 30 + (score / 100) * 70;
  // Convert hex to HSL and adjust saturation
  const hex = baseColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const add = max + min;
  const l = add * 0.5;

  let h = 0;
  if (diff !== 0) {
    switch (max) {
      case r:
        h = (g - b) / diff + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }

  return `hsl(${Math.round(h * 360)}, ${saturation.toFixed(2)}%, ${Math.round(l * 100)}%)`;
};

// Get dynamic color with alpha
const getDynamicColorWithAlpha = (baseColor: string, score: number, alpha: number) => {
  // Convert score to saturation (0-100 -> 30-100)
  const saturation = 30 + (score / 100) * 70;
  // Convert hex to HSL and adjust saturation
  const hex = baseColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const add = max + min;
  const l = add * 0.5;

  let h = 0;
  if (diff !== 0) {
    switch (max) {
      case r:
        h = (g - b) / diff + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }

  return `hsla(${Math.round(h * 360)}, ${saturation.toFixed(2)}%, ${Math.round(l * 100)}%, ${alpha})`;
};

// Handle slider events
const handleSliderInput = (scoreType: string, value: number) => {
  scores[scoreType] = value;
  triggerHapticFeedback(value);
};

const handleSliderMouseDown = (scoreType: string) => {
  isDragging[scoreType] = true;
};

const handleSliderMouseUp = (scoreType: string) => {
  if (isDragging[scoreType]) {
    isDragging[scoreType] = false;
    // Trigger shake animation on release
    triggerShakeAnimation(scoreType, scores[scoreType]);
    // Update score via API
    updateScore(scoreType, scores[scoreType]);
  }
};

// Calculate thumb position for icon alignment
const getThumbPosition = (score: number) => {
  // Slider thumb is 28px wide, so we need to account for that
  // The thumb center moves from 14px to (100% - 14px)
  const thumbWidth = 28;
  const percentage = score / 100;
  return `calc(${percentage * 100}% - ${percentage * thumbWidth}px + ${thumbWidth / 2}px)`;
};
</script>

<template>
  <div :class="cn('grid grid-cols-5 gap-0.5 p-2')">
    <div
      v-for="scoreDef in scoreDefinitions"
      :key="scoreDef.key"
      :class="
        cn(
          'flex items-center gap-2 transition-transform duration-200',
          shakeAnimations[scoreDef.key] ? 'animate-shake' : '',
        )
      "
    >
      <!-- Label -->
      <div :class="cn('flex w-10 flex-shrink-0 items-center gap-2')">
        <span :class="cn('text-xs font-medium text-gray-300')">
          {{ scoreDef.label }}
        </span>
      </div>

      <!-- Slider with icon thumb -->
      <div :class="cn('relative flex-1')">
        <input
          :value="scores[scoreDef.key]"
          type="range"
          min="0"
          max="100"
          step="1"
          :class="
            cn(
              'h-3 w-full cursor-pointer appearance-none rounded-lg',
              'bg-gray-700 transition-colors duration-200 outline-none',
              'icon-slider-thumb',
            )
          "
          :style="{
            background: `linear-gradient(to right,
            ${getDynamicColorWithAlpha(scoreDef.color, scores[scoreDef.key], 0.4)}  0%,
            ${getDynamicColor(scoreDef.color, scores[scoreDef.key])}                ${scores[scoreDef.key]}%,
            #374151                                                                 ${scores[scoreDef.key]}%,
            #374151                                                                 100%)`,
            '--thumb-color': getDynamicColor(scoreDef.color, scores[scoreDef.key]),
          }"
          @input="
            e => handleSliderInput(scoreDef.key, Number((e.target as HTMLInputElement).value))
          "
          @mousedown="handleSliderMouseDown(scoreDef.key)"
          @mouseup="handleSliderMouseUp(scoreDef.key)"
          @touchstart="handleSliderMouseDown(scoreDef.key)"
          @touchend="handleSliderMouseUp(scoreDef.key)"
        />

        <!-- Icon overlay for thumb -->
        <div
          :class="cn('pointer-events-none absolute top-1/2 -translate-x-1/2 -translate-y-1/2')"
          :style="{
            left: getThumbPosition(scores[scoreDef.key]),
            color:
              feedbackStates[scoreDef.key] === 'success'
                ? '#10B981'
                : feedbackStates[scoreDef.key] === 'error'
                  ? '#EF4444'
                  : '#1f2937',
            fontSize: '14px',
            textShadow: '0 1px 2px rgba(0,0,0,0.5)',
          }"
        >
          <div v-if="feedbackStates[scoreDef.key] === 'loading'">⌛</div>
          <span v-else-if="feedbackStates[scoreDef.key] === 'success'">🎉</span>
          <span v-else-if="feedbackStates[scoreDef.key] === 'error'">❌</span>
          <span v-else>{{ scoreDef.icon }}</span>
        </div>
      </div>

      <!-- Score value -->
      <span
        :class="
          cn('w-8 rounded-full px-1 text-center text-xs font-bold transition-colors duration-200')
        "
        :style="{
          backgroundColor: getDynamicColorWithAlpha(scoreDef.color, scores[scoreDef.key], 0.2),
          color: getDynamicColor(scoreDef.color, scores[scoreDef.key]),
          border: `1px solid ${getDynamicColorWithAlpha(scoreDef.color, scores[scoreDef.key], 0.4)}`,
        }"
      >
        {{ scores[scoreDef.key] }}
      </span>
    </div>
  </div>
</template>

<style scoped>
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.icon-slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: var(--thumb-color, #8b5cf6);
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.icon-slider-thumb::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.icon-slider-thumb::-moz-range-thumb {
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: var(--thumb-color, #8b5cf6);
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.icon-slider-thumb::-moz-range-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}
</style>
