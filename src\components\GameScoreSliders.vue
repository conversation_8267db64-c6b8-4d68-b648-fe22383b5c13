<script setup lang="ts">
import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';
import type { GameSteamWithRating } from '@/stores/item';

const props = defineProps<{
  game: GameSteamWithRating;
}>();

const emit = defineEmits<{
  (e: 'scoreUpdated', scoreType: string, score: number): void;
}>();

// Score definitions with labels and colors
const scoreDefinitions = [
  { key: 'scoreAudio', label: 'Audio', icon: '🎵', color: '#8B5CF6' },
  { key: 'scoreBias', label: 'Bias', icon: '⚖️', color: '#EF4444' },
  { key: 'scoreBuy', label: 'Buy', icon: '💰', color: '#10B981' },
  { key: 'scoreControl', label: 'Control', icon: '🎮', color: '#3B82F6' },
  { key: 'scoreCreative', label: 'Creative', icon: '🎨', color: '#F59E0B' },
  { key: 'scoreFun', label: 'Fun', icon: '😄', color: '#EC4899' },
  { key: 'scoreGraphic', label: 'Graphic', icon: '🖼️', color: '#06B6D4' },
  { key: 'scorePotential', label: 'Potential', icon: '🚀', color: '#84CC16' },
  { key: 'scoreStability', label: 'Stability', icon: '🛡️', color: '#6B7280' },
  { key: 'scoreUi', label: 'UI', icon: '📱', color: '#8B5CF6' },
] as const;

// Reactive scores object
const scores = ref<Record<string, number>>({});

// Track which sliders are being dragged
const isDragging = ref<Record<string, boolean>>({});

// Track shake animations
const shakeAnimations = ref<Record<string, boolean>>({});

// Initialize scores from game's tierReview
watchEffect(() => {
  if (props.game?.tierReview) {
    scoreDefinitions.forEach(({ key }) => {
      scores.value[key] = props.game.tierReview?.[key] ?? 0;
      isDragging.value[key] = false;
      shakeAnimations.value[key] = false;
    });
  } else {
    // Default scores if no tierReview exists
    scoreDefinitions.forEach(({ key }) => {
      scores.value[key] = 0;
      isDragging.value[key] = false;
      shakeAnimations.value[key] = false;
    });
  }
});

// Update function (called when mouse is released)
const updateScore = async (scoreType: string, score: number) => {
  try {
    await trpc.tierReview.updateScore.mutate({
      gameSteamId: props.game.steamId,
      scoreType: scoreType as any,
      score: score,
    });
    emit('scoreUpdated', scoreType, score);
  } catch (error) {
    console.error('Failed to update score:', error);
  }
};

// Haptic feedback and shake animation
const triggerFeedback = (scoreType: string, intensity: number) => {
  // Haptic feedback
  if ('vibrate' in navigator) {
    // Scale vibration duration based on score (higher score = longer vibration)
    const duration = Math.floor((intensity / 100) * 200) + 50; // 50-250ms
    navigator.vibrate(duration);
  }

  // Shake animation - more intense for higher scores
  shakeAnimations.value[scoreType] = true;
  setTimeout(
    () => {
      shakeAnimations.value[scoreType] = false;
    },
    300 + (intensity / 100) * 200,
  ); // 300-500ms shake duration
};

// Get dynamic color based on score
const getDynamicColor = (baseColor: string, score: number) => {
  // Convert score to saturation (0-100 -> 30-100)
  const saturation = 30 + (score / 100) * 70;
  // Convert hex to HSL and adjust saturation
  const hex = baseColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const add = max + min;
  const l = add * 0.5;

  let h = 0;
  if (diff !== 0) {
    switch (max) {
      case r:
        h = (g - b) / diff + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }

  return `hsl(${Math.round(h * 360)}, ${saturation.toFixed(2)}%, ${Math.round(l * 100)}%)`;
};

// Handle slider events
const handleSliderInput = (scoreType: string, value: number) => {
  scores.value[scoreType] = value;
  triggerFeedback(scoreType, value);
};

const handleSliderMouseDown = (scoreType: string) => {
  isDragging.value[scoreType] = true;
};

const handleSliderMouseUp = (scoreType: string) => {
  if (isDragging.value[scoreType]) {
    isDragging.value[scoreType] = false;
    updateScore(scoreType, scores.value[scoreType]);
  }
};
</script>

<template>
  <div
    :class="
      cn(
        'grid grid-cols-2 gap-3 p-4',
        'bg-gray-900/95 backdrop-blur-md',
        'rounded-lg border border-gray-700',
        'max-h-96 overflow-y-auto',
      )
    "
  >
    <div
      v-for="scoreDef in scoreDefinitions"
      :key="scoreDef.key"
      :class="
        cn(
          'flex flex-col gap-2 transition-transform duration-200',
          shakeAnimations[scoreDef.key] ? 'animate-shake' : '',
        )
      "
    >
      <!-- Score label and value -->
      <div :class="cn('flex items-center justify-between')">
        <div :class="cn('flex items-center gap-2')">
          <span :class="cn('text-lg')">{{ scoreDef.icon }}</span>
          <span :class="cn('text-sm font-medium text-gray-300')">
            {{ scoreDef.label }}
          </span>
        </div>
        <span
          :class="cn('rounded-full px-2 py-1 text-sm font-bold transition-colors duration-200')"
          :style="{
            backgroundColor: `${getDynamicColor(scoreDef.color, scores[scoreDef.key])}20`,
            color: getDynamicColor(scoreDef.color, scores[scoreDef.key]),
            border: `1px solid ${getDynamicColor(scoreDef.color, scores[scoreDef.key])}40`,
          }"
        >
          {{ scores[scoreDef.key] }}
        </span>
      </div>

      <!-- Slider -->
      <div :class="cn('relative')">
        <input
          :value="scores[scoreDef.key]"
          type="range"
          min="0"
          max="100"
          step="1"
          :class="
            cn(
              'h-2 w-full cursor-pointer appearance-none rounded-lg',
              'bg-gray-700 transition-all duration-200 outline-none',
              'slider-thumb',
            )
          "
          :style="{
            background: `linear-gradient(to right, 
            ${getDynamicColor(scoreDef.color, scores[scoreDef.key])}  0%, 
            ${getDynamicColor(scoreDef.color, scores[scoreDef.key])}  ${scores[scoreDef.key]}%,
            #374151                                                   ${scores[scoreDef.key]}%, 
            #374151                                                   100%)`,
          }"
          @input="
            e => handleSliderInput(scoreDef.key, Number((e.target as HTMLInputElement).value))
          "
          @mousedown="handleSliderMouseDown(scoreDef.key)"
          @mouseup="handleSliderMouseUp(scoreDef.key)"
          @touchstart="handleSliderMouseDown(scoreDef.key)"
          @touchend="handleSliderMouseUp(scoreDef.key)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: currentColor;
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider-thumb::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.slider-thumb::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: currentColor;
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider-thumb::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}
</style>
