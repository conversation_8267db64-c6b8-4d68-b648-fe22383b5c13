<script setup lang="ts">
import * as _ from 'lodash-es';
import { type DataTableFilterEvent, type DataTableSortEvent } from 'primevue/datatable';
import Paginator, { type PageState } from 'primevue/paginator';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';

import { createReusableTemplate } from '@vueuse/core';

import { cn } from '@/helpers/cn';
import type { SimpleTierItemObject, SimpleTierSetInstance } from '@/server/routers/simpleTier';
import { trpc } from '@/services/trpc';
import { type GameSteamWithRating, pinia, useItemStore } from '@/stores/item';

import GameFilters, { type GameFilterType } from './GameFilters.vue';
import OfficeSelectedTierLanes from './OfficeSelectedTierLanes.vue';
import OfficeTableRow from './OfficeTableRow.vue';
import OfficeToolbar from './OfficeToolbar.vue';

const toast = useToast();
const [DefinePaginatorTemplate, ReusePaginatorTemplate] = createReusableTemplate();

const itemStore = useItemStore(pinia);
const { focusedGame } = storeToRefs(itemStore);

const games = ref<GameSteamWithRating[]>([]);
const isLoading = ref(false);

const pageSize = ref(20);
const totalGames = ref(0);
const skip = ref(0);

const connectedToDb = ref(true);
provide('connectedToDb', connectedToDb);

const confirm = useConfirm();

const tierLanes = ref<Awaited<ReturnType<typeof trpc.tierLane.retrieveMany.query>>>([]);

const selectedTierLanes = ref<string[]>([]);

const editingTierLanes = computed(
  () =>
    _.groupBy(
      selectedTierLanes.value
        .map(slug => tierLanes.value.find(tl => tl.slug === slug))
        .filter(Boolean),
      'tierSetSlug',
    ) as Record<string, typeof tierLanes.value>,
);

async function retrieveTierLanes() {
  tierLanes.value = await trpc.tierLane.retrieveMany.query({
    spaces: [1],
    sorts: [
      { type: 'FIELD', value: 'score', order: -1 },
      { type: 'FIELD', value: 'label', order: 1 },
    ],
  });
  if (selectedTierLanes.value.length === 0) {
    selectedTierLanes.value = [
      ...tierLanes.value.filter(tl => tl.tierSetSlug === 'hypeness'),
      ...tierLanes.value.filter(tl => tl.tierSetSlug === 'suckz'),
      ...tierLanes.value.filter(tl => tl.tierSetSlug === 'games-good-good'),
    ].map(tl => tl.slug);
  }
}

function setGameListResult(dataList: Awaited<ReturnType<typeof trpc.gameSteam.list.query>>) {
  const { data, total } = dataList;

  games.value = data.map(game => {
    const exist: GameSteamWithRating = { ...game, rating: {}, galleriaItems: [] };
    exist.tierItems?.forEach((ti, i) => {
      exist.rating[ti.tierLaneSlug] = true;
      exist.tierItems[i].tierLane = tierLanes.value.find(tl => tl.slug === ti.tierLaneSlug);
    });

    const items: GameSteamWithRating['galleriaItems'] = [
      ...game.movies.map((m, i) => ({
        name: m.name || `Movie ${i + 1}`,
        teaser: m.teaser!,
        // img: m.thumbnail, // Note: some thumbnail from steam already broken
        video: m.webm[480],
      })),
      ...game.screenshots.map(s => ({ img: s.path_full })),
    ];

    if (game.hasValidImageWide) {
      items.push({
        img: game.imageFinalWide!,
      });
    }

    if (game.hasValidImageTall) {
      items.push({
        img: game.imageFinalTall!,
      });
    }

    exist.galleriaItems = items;
    return exist;
  });
  totalGames.value = total;
}

const latestFilters = ref<GameFilterType>();

async function fetch(page: number, changedFilters?: GameFilterType) {
  if (isLoading.value) {
    return;
  }
  try {
    isLoading.value = true;

    const url = new URL(window.location.href);
    if (!latestFilters.value && url.searchParams.has('p')) {
      page = Number(url.searchParams.get('p')) - 1;
    }

    const skip = page * pageSize.value;

    const f = changedFilters || latestFilters.value!;

    if (f.search) {
      console.log(f);
      const dataList = await trpc.gameSteam.search.query({
        search: f.search,
      });
      setGameListResult(dataList);
      isLoading.value = false;
      return;
    }

    latestFilters.value = f;

    console.log('Fetching:', JSON.parse(JSON.stringify(f)));

    const [dataList] = await Promise.all([
      trpc.gameSteam.list.query({
        filters: f.filters,
        sorts: f.sorts,
        skip,
        take: pageSize.value,
      }),
    ]);

    setGameListResult(dataList);

    window.scrollTo(0, 0);

    url.searchParams.set('p', `${page + 1}`);
    window.history.replaceState({}, '', url.toString());
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't search",
      detail: 'Maybe the tier is too big :(',
      life: 20000,
    });
  }
  isLoading.value = false;
}
const debouncedFetch = _.debounce(fetch, 1_000);

function rateFocusedGame(tierLaneSlug: string) {
  const rowRef = rowRefs.value.find(ref => ref.$props.game.steamId === focusedGame.value?.steamId);
  if (!rowRef) return;

  rowRef.selectTierLane(tierLaneSlug);
}

async function onSort(e: DataTableSortEvent) {
  debouncedFetch(0);
}
async function onFilter(e: DataTableFilterEvent) {
  debouncedFetch(0);
}

const expandedRows = ref<Record<number, boolean> | null>([]);
function expandAll() {
  expandedRows.value = games.value.reduce(
    (acc: Record<number, boolean>, p) => (acc[`${p.steamId}`] = true) && acc,
    {},
  );
}
function collapseAll() {
  expandedRows.value = null;
}

async function exportGamesToSimpleTier() {
  const groups = _.groupBy(games.value, game => {
    const suckz = game.tierItems.find(ti => ti.tierLaneSlug.startsWith('suckz-'));
    return suckz?.tierLaneSlug || '';
  });
  const lanes = [
    ...(editingTierLanes.value.suckz?.map(tl => tl.slug) || [
      'suckz-s',
      'suckz-u',
      'suckz-c',
      'suckz-k',
      'suckz-z',
    ]),
    '',
  ];
  const simpleTier: SimpleTierSetInstance = {
    label: `Exported from office table ${new Date().toLocaleString()}`,
    canEdit: true,
    id: 1,
    lexorank: '',
    spaceId: null,
    jsonObject: {
      items: [
        ...lanes.map((group, i) => {
          const tier = tierLanes.value.find(tl => tl.slug === group);

          return {
            id: +i,
            label: tier?.label || group,
            mainColor: tier?.mainColor || '#000',
            textColor: tier?.textColor || '#fff',
            items: (groups[group] || []).map(g => {
              const item = {
                id: g.steamId,
                label: g.gameName,
                urls: [g.gameUrl],
                refs: [
                  {
                    url: g.imageFinalTall || g.imageFinalWide!,
                  },
                ],
              } satisfies SimpleTierItemObject;

              if (g.screenshots?.[0]?.path_full) {
                item.refs.push({
                  url: g.screenshots[0].path_full,
                });
              }

              if (g.movies?.[0]?.teaser) {
                item.refs.push({
                  url: g.movies[0].teaser,
                });
              }

              return item;
            }),
          };
        }),
      ],
    },
  };

  const blob = new Blob([JSON.stringify(simpleTier)], { type: 'text/json' });
  const link = document.createElement('a');

  link.download = 'games-for-simple-tier.json';
  link.href = window.URL.createObjectURL(blob);
  link.dataset.downloadurl = ['text/json', link.download, link.href].join(':');

  const evt = new MouseEvent('click', {
    view: window,
    bubbles: true,
    cancelable: true,
  });

  link.dispatchEvent(evt);
  link.remove();
}

const rowRefs = ref<InstanceType<typeof OfficeTableRow>[]>([]);

function scrollToNextGame(currentIndex: number) {
  // Find the next game element
  const nextIndex = currentIndex + 1;
  if (nextIndex < games.value.length && rowRefs.value[nextIndex]) {
    // Scroll to the next game with smooth animation
    nextTick(() => {
      rowRefs.value[nextIndex].$el.parentElement!.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    });
  }
}

watch(pageSize, () => debouncedFetch(0));

onMounted(() => {
  retrieveTierLanes();
});
</script>

<template>
  <PrimeToast />
  <OfficeToolbar
    v-if="tierLanes.some(tl => tl.canEdit)"
    :tierLanes="tierLanes"
    :editingTierLanes="editingTierLanes"
    @rated="rateFocusedGame"
  />

  <!-- Filters -->
  <GameFilters :tierLanes="tierLanes" :disabled="isLoading" @search="e => debouncedFetch(0, e)">
    <template #append>
      <div class="mb-2">
        <div>Selected Tier Lanes:</div>
        <OfficeSelectedTierLanes
          v-model:selectedTierLanes="selectedTierLanes"
          :tierLanes="tierLanes"
          :disabled="isLoading"
        />
      </div>
    </template>
  </GameFilters>

  <DefinePaginatorTemplate>
    <Paginator
      :page="skip / pageSize"
      v-model:rows="pageSize"
      :rowsPerPageOptions="[1, 20, 50, 100, 200, 500, 1000]"
      :totalRecords="totalGames"
      @page="(e: PageState) => debouncedFetch(e.page)"
    >
      <template #start>
        <div>{{ totalGames.toLocaleString() }} games</div>
      </template>
      <template #end>
        <PrimeButton class="text-xxs" label="จัด" @click="exportGamesToSimpleTier" />
      </template>
    </Paginator>
  </DefinePaginatorTemplate>

  <ReusePaginatorTemplate />
  <div class="mb-8"></div>

  <!-- MAIN GAMES TABLE -->
  <div :class="cn('group/games flex flex-row flex-wrap gap-1')">
    <div
      v-for="(_, i) in games"
      :class="cn('relative', isLoading ? 'pointer-events-none opacity-30' : '')"
    >
      <OfficeTableRow
        v-model:game="games[i]"
        ref="rowRefs"
        :tierLanes="tierLanes"
        @rated="scrollToNextGame(i)"
      />
    </div>
  </div>

  <div class="mb-8"></div>
  <ReusePaginatorTemplate />
  <div class="mb-16"></div>
</template>

<style scoped lang="css">
@reference '@/styles/global.css';
@keyframes preview-show {
  0% {
    position: static;
  }
  100% {
    position: absolute;
    z-index: 50;
    margin: 0.25rem;
    /* transform: translateY(-50%); */
    background-color: var(--color-theme-bg);
    box-shadow: 0 0 0 2px #f7dc6f;
  }
}

/* .preview-wrapper:hover {
  animation-name: preview-show;
  animation-delay: 0.2s;
  animation-duration: 0.2s;
  animation-fill-mode: forwards;
} */

:deep(tr) {
  @apply max-sm:flex max-sm:flex-col;
}
</style>
