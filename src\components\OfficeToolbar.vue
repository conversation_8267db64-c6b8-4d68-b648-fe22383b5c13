<script setup lang="ts">
import { cn } from '@/helpers/cn';
import { type trpc } from '@/services/trpc';
import { pinia, useItemStore } from '@/stores/item';

import GameScoreSliders from './GameScoreSliders.vue';

defineProps<{
  tierLanes: Awaited<ReturnType<typeof trpc.tierLane.retrieveMany.query>>;
  editingTierLanes: Record<string, Awaited<ReturnType<typeof trpc.tierLane.retrieveMany.query>>>;
}>();

const emit = defineEmits<{
  (e: 'rated', tierLaneSlug: string): void;
}>();

const itemStore = useItemStore(pinia);
const { focusedGame: game } = storeToRefs(itemStore);

// State for showing score sliders
const showScoreSliders = ref(false);

// Add this method to rate the currently focused game
function rateSelectedGame(tierLaneSlug: string) {
  if (game.value !== null) {
    emit('rated', tierLaneSlug);
  }
}

// Handle score updates
function handleScoreUpdated(scoreType: string, score: number) {
  if (game.value?.tierReview) {
    // Update the local game data optimistically
    (game.value.tierReview as any)[scoreType] = score;
  }
}

// Toggle score sliders visibility
function toggleScoreSliders() {
  showScoreSliders.value = !showScoreSliders.value;
}
</script>

<template>
  <!-- Floating rating tray at bottom of screen -->
  <div
    v-if="game"
    :class="
      cn(
        'fixed right-0 bottom-0 left-0 z-50 w-screen',
        'bg-gray-900/90 backdrop-blur-md',
        'border-t border-gray-700 p-2 shadow-lg',
        'flex flex-wrap justify-center gap-2',
      )
    "
  >
    <div
      v-for="lanes in editingTierLanes"
      :class="
        cn(
          'flex flex-col items-center',
          'rounded border-2 border-transparent transition-all hover:border-gray-700', // Added padding for hover area
        )
      "
    >
      <!-- <div :class="cn('mb-1 text-xs text-gray-400')">
        {{ setSlug }}
      </div> -->
      <div :class="cn('flex flex-row gap-1')">
        <button
          v-for="lane in lanes"
          :key="lane.slug"
          :class="
            cn(
              'group/lane',
              'relative flex cursor-pointer items-center justify-center', // Center icon/text
              'transition-all hover:scale-110', // Reduced scale for less aggressive hover
              game.rating[lane.slug] ? 'opacity-100' : 'opacity-50',
              'rounded-md', // Rounded buttons
              'h-15 w-15', // Fixed button size
            )
          "
          :style="{
            '--main': lane.mainColor || 'var(--color-primary)',
            '--text': lane.textColor,
          }"
          :disabled="!lane.canEdit"
          type="button"
          v-tooltip.top="{
            value: lane.label,
            pt: {
              text: 'font-black! bg-black/50!',
            },
          }"
          @click="() => rateSelectedGame(lane.slug)"
        >
          <div
            :class="
              cn(
                'absolute top-1 right-0 left-0 h-2 w-2/3 justify-self-center',
                'rounded-full shadow-(--main)',
                'animate-glow group-hover/lane:animate-ping',
                !lane.canEdit ? 'animate-fill-forwards animate-once' : '',
                game.rating[lane.slug]
                  ? 'shadow-md' // Reduced shadow intensity
                  : 'shadow-sm grayscale hover:grayscale-0',
              )
            "
          ></div>
          <div
            :class="
              cn(
                'flex items-center justify-center text-5xl shadow-(--main) hover:grayscale-0', // Smaller icon area
                game.rating[lane.slug]
                  ? 'animate-glow grayscale-0'
                  : 'group-hover/lane:animate-glow grayscale-[80%] hover:grayscale-[50%]',
              )
            "
          >
            <span v-if="lane.icon">{{ lane.icon }}</span>
            <span v-else-if="lane.label" :class="cn('text-(--text)')">
              {{ lane.label.charAt(0) }}
            </span>
          </div>
        </button>
      </div>
    </div>

    <!-- Score Sliders Toggle Button -->
    <button
      :class="
        cn(
          'flex items-center justify-center self-end',
          'h-15 w-15 cursor-pointer rounded-md',
          'border-2 transition-all',
          showScoreSliders
            ? 'bg-purple-600/20 hover:bg-purple-600/40'
            : 'bg-purple-800/20 hover:bg-purple-800/40',
          showScoreSliders ? 'border-purple-400' : 'border-transparent hover:border-purple-600',
          'text-purple-400 hover:text-purple-300',
        )
      "
      type="button"
      v-tooltip.top="{
        value: 'Score Sliders',
        pt: {
          text: 'font-black! bg-black/50!',
        },
      }"
      @click="toggleScoreSliders"
    >
      <span class="text-2xl">💯</span>
    </button>
  </div>

  <!-- Score Sliders Panel (above the toolbar) -->
  <div
    v-if="game && showScoreSliders"
    :class="cn('fixed right-4 bottom-20 z-50', 'w-96 max-w-[calc(100vw-2rem)]')"
  >
    <GameScoreSliders :game="game" @score-updated="handleScoreUpdated" />
  </div>
</template>
